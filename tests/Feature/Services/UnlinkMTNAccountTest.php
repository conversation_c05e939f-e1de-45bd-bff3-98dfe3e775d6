<?php

namespace Tests\Feature\Services;

use App\Models\Customer;
use App\Models\Transaction;
use App\Services\MtnApiService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;
use Mockery;
use Tests\TestCase;

class UnlinkMTNAccountTest extends TestCase
{
    use RefreshDatabase;

    private MtnApiService $service;
    private MockHandler $mockHandler;
    private Client $client;
    private Customer $customer;
    private Transaction $transaction;

    /**
     * @return string
     */
    public function successfulXmlResponse(): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?><ns0:unlinkfinancialresourceinformationresponse xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend"/>';
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        // Set up HTTP mocking
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);
        $this->client = new Client(['handler' => $handlerStack]);

        // Create a partial mock of the service that returns our mocked client
        $this->service = Mockery::mock(MtnApiService::class, ['test'])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        // Mock the getClient method to return our mocked client
        $this->service->shouldReceive('getClient')
            ->andReturn($this->client);

        // Set up test data
        $this->setupTestData();
    }

    private function setupTestData(): void
    {
        $this->customer = Customer::factory()->withOptions()->create();
        $this->transaction = Transaction::factory()->create([
            'Telephone_Number' => $this->customer->Telephone_Number
        ]);
    }

    public function test_unlink_method_is_successful()
    {
        // Mock successful response (empty body indicates success)
        $this->mockHandler->append(new Response(200, ['Content-Type' => 'application/xml'], $this->successfulXmlResponse()));

        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
        '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
        '    <fri>FRI:'. $this->customer->optional_savings_account_number . '@WKD-SAVINGS/SP</fri>' . PHP_EOL .
        '    <msisdn>' . $this->customer->Telephone_Number . '</msisdn>' . PHP_EOL .
        '</ns0:unlinkfinancialresourceinformationrequest>';

        $this->assertTrue($this->service->unlink($payload));
    }

    public function test_opting_out_customer_is_successful()
    {
        $this->mockHandler->append(new Response(200, ['Content-Type' => 'application/xml'], $this->successfulXmlResponse()));
        $result = $this->service->unlinkCustomer($this->customer);

        $this->assertTrue($result);
    }

    public function test_opting_out_customer_fails_without_savings_account()
    {
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $response = new Response(400, [], 'Bad Request');
        $exception = new ValidationException('Bad Request', $request, $response);


        $customer = Customer::factory()->create();
        $result = $this->service->unlinkCustomer($customer);

        $this->assertFalse($result);
    }

    public function test_unlink_method_handles_error_response_with_details()
    {
        // Mock response with error details
        $errorXml = '<?xml version="1.0" encoding="UTF-8"?>
          <ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="INTERNAL_SERVER_ERROR">
            <arguments name="id" value="Sampe error message"/>
          </ns0:errorResponse>';

        $this->mockHandler->append(new Response(200, ['Content-Type' => 'application/xml'], $errorXml));

        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
            '    <fri>FRI:TEST123@WKD-SAVINGS/SP</fri>' . PHP_EOL .
            '    <msisdn>256700123456</msisdn>' . PHP_EOL .
            '</ns0:unlinkfinancialresourceinformationrequest>';

        $result = $this->service->unlink($payload);

        $this->assertFalse($result);
    }

    public function test_unlink_method_handles_request_exception()
    {
        // Mock request exception
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $response = new Response(400, [], 'Bad Request');
        $exception = new RequestException('Bad Request', $request, $response);

        $this->mockHandler->append($exception);

        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
            '    <fri>FRI:TEST123@WKD-SAVINGS/SP</fri>' . PHP_EOL .
            '    <msisdn>256700123456</msisdn>' . PHP_EOL .
            '</ns0:unlinkfinancialresourceinformationrequest>';

        $result = $this->service->unlink($payload);

        $this->assertFalse($result);
    }

    public function test_unlink_method_handles_general_exception()
    {
        // Mock general exception
        $this->mockHandler->append(new \Exception('Network error'));

        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
            '    <fri>FRI:TEST123@WKD-SAVINGS/SP</fri>' . PHP_EOL .
            '    <msisdn>256700123456</msisdn>' . PHP_EOL .
            '</ns0:unlinkfinancialresourceinformationrequest>';

        $result = $this->service->unlink($payload);

        $this->assertFalse($result);
    }

    public function test_unlink_customer_returns_false_on_failure()
    {
        // Mock request exception
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $response = new Response(500, [], 'Internal Server Error');
        $exception = new RequestException('Internal Server Error', $request, $response);

        $this->mockHandler->append($exception);

        $result = $this->service->unlinkCustomer($this->customer);

        $this->assertFalse($result);
    }

    public function test_unlink_loan_returns_false_when_unlink_fails()
    {
        // Mock the app to be in production environment (not local)
        $this->app->instance('env', 'production');

        // Set up HTTP mocking for the production service
        $mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($mockHandler);
        $client = new Client(['handler' => $handlerStack]);

        // Create a partial mock of the service that returns our mocked client
        $prodService = Mockery::mock(MtnApiService::class, ['test'])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        // Mock the getClient method to return our mocked client
        $prodService->shouldReceive('getClient')
            ->andReturn($client);

        // Mock request exception
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $response = new Response(400, [], 'Bad Request');
        $exception = new RequestException('Bad Request', $request, $response);

        $mockHandler->append($exception);

        $result = $prodService->unlinkLoan($this->transaction);

        $this->assertFalse($result);

        // Verify that customer options were not updated (save should not be called on failure)
        // The customer mock expects save() to be called only on success
    }

    public function test_unlink_loan_does_not_update_customer_options_when_unlink_fails()
    {
        // Mock the app to be in production environment (not local)
        $this->app->instance('env', 'production');

        // Set up HTTP mocking for the production service
        $mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($mockHandler);
        $client = new Client(['handler' => $handlerStack]);

        // Create a partial mock of the service that returns our mocked client
        $prodService = Mockery::mock(MtnApiService::class, ['test'])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        // Mock the getClient method to return our mocked client
        $prodService->shouldReceive('getClient')
            ->andReturn($client);

        // Mock error response with details
        $errorXml = '<?xml version="1.0" encoding="UTF-8"?>
          <ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="INTERNAL_SERVER_ERROR">
            <arguments name="id" value="Sampe error message"/>
          </ns0:errorResponse>';

        $mockHandler->append(new Response(200, [], $errorXml));

        $result = $prodService->unlinkLoan($this->transaction);

        $this->assertFalse($result);

        // Verify that customer options were not changed (save should not be called on failure)
        // The customer mock expects save() to be called only on success
    }

    public function test_unlink_method_handles_request_exception_without_response()
    {
        // Mock request exception without response
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $exception = new RequestException('Connection timeout', $request);

        $this->mockHandler->append($exception);

        $payload = '<test>payload</test>';

        $result = $this->service->unlink($payload);

        $this->assertFalse($result);
    }
}
