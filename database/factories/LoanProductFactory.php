<?php

namespace Database\Factories;

use App\Models\LoanProduct;
use App\Models\LoanProductType;
use App\Models\Partner;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoanProduct>
 */
class LoanProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LoanProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'Partner_ID' => Partner::factory(),
            'code' => 'LP-' . fake()->unique()->ean8(),
            'name' => fake()->words(3, true) . ' Loan',
            'loan_product_type_id' => (LoanProductType::create([
                'name' => 'Mobile Loan',
                'code' => '1001',
            ]))->id,
            'minimum_principal_amount' => 10000,
            'default_principal_amount' => 50000,
            'maximum_principal_amount' => 500000,
            'auto_debit' => 'No',
            'decimal_place' => 0,
            'round_up_or_off_all_interest' => 1,
            'repayment_order' => '["Penalty","Interest","Principal","Fees"]',
            'arrears_auto_write_off_days' => 180,

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'Active',
            ];
        });
    }

    /**
     * Configure the model factory to create a personal loan product.
     *
     * @return $this
     */
    public function personal()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'Personal',
                'name' => 'Personal Loan',
            ];
        });
    }

    /**
     * Configure the model factory to create a business loan product.
     *
     * @return $this
     */
    public function business()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'Business',
                'name' => 'Business Loan',
                'minimum_trading_period' => 6,
            ];
        });
    }

    /**
     * Configure the model factory to create a small loan product.
     *
     * @return $this
     */
    public function smallLoan()
    {
        return $this->state(function (array $attributes) {
            return [
                'minimum_amount' => 10000,
                'maximum_amount' => 500000,
                'minimum_tenure' => 1,
                'maximum_tenure' => 3,
            ];
        });
    }

    /**
     * Configure the model factory to create a large loan product.
     *
     * @return $this
     */
    public function largeLoan()
    {
        return $this->state(function (array $attributes) {
            return [
                'minimum_amount' => 500000,
                'maximum_amount' => 10000000,
                'minimum_tenure' => 3,
                'maximum_tenure' => 24,
                'guarantor_required' => true,
                'collateral_required' => true,
            ];
        });
    }
}
